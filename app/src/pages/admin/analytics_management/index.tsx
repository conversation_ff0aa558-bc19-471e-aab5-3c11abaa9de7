import React, { useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import {
  CandidatesRegisteredCard,
  StatsCard,
  HiringAnalyticsCard,
  LastestJobsPostedCard,
  TodayInterviewSchedule,
  JobStatsChartCard,
} from "@src/components/WildCard/AnalyticsManagement";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { KeyPairInterface } from "@src/redux/interfaces";
import { ExportAnalytics } from "@src/components/WildCard/AnalyticsManagement";
import { Tune } from "@mui/icons-material";
import { Badge } from "antd";
import { Button } from "react-bootstrap";
import { useAppDispatch } from "@src/redux/store";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";


interface AnalyticsSearchParams {
  department_id?: number;
  opportunity_id?: number;
  date_from?: string;
  date_to?: string;
  status?: string;
}

const settings = {
  dots: true,
  infinite: false,
  speed: 500,
  slidesToShow: 2,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 820,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
  ],
};

type AnalyticsManagementPageProps = {
  pageDetail: KeyPairInterface;
  filters: AnalyticsSearchParams;
};

export default function AnalyticsManagementPage({
  pageDetail,
  filters,
}: AnalyticsManagementPageProps) {
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");
  const interviewsPermissions =
    useEmployeeSelectedPagePermissions("interviews");

  const dispatch = useAppDispatch();
  const router = useRouter();

  // Initialize filter state from URL parameters
  const [filterState, setFilterState] = useState<KeyPairInterface>(
    filters as KeyPairInterface,
  );
  const [filterCount, setFilterCount] = useState<number>(0);

  // Update URL when filters change and count filter values
  useEffect(() => {
    let queryParams: AnalyticsSearchParams = {};

    if (filterState) {
      queryParams = { ...queryParams, ...filterState };
    }

    router.push(
      { pathname: APP_ROUTE.ANALYTICS_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );

    if (filterState) {
      const filteredPairs = Object.entries(filterState).filter(
        ([key, value]) => value != null && key !== "save_history",
      );
      setFilterCount(filteredPairs.length);
    } else {
      setFilterCount(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterState]);

  // Function to handle filter changes
  const setFilters = async (filters: KeyPairInterface) => {
    await setFilterState(filters);

    // TODO: Apply filters to analytics data
    console.log("Applied filters:", filters);

    // Here you would typically refresh your analytics data
    // await fetchAnalyticsData(filters);
  };

  const handleFilterModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.ANALYTICS_FILTER_MODAL,
        options: {
          title: "Analytics Filters",
          filters: filterState,
          onConfirm: setFilters,
        },
      }),
    );
  };

  return (
    <section className="hiring">
      <PageHeader
        pageTitle={pageDetail?.title ?? "Analytics Management"}
        buttonComponent={
        <>
          <Button
            onClick={handleFilterModal}
            className="btn btn-border d-flex align-items-center justify-content-between px-3 group">
            <Tune className="me-1 " />
            <span>Filters</span>
            <Badge
              count={filterCount}
              showZero={false}
              dot={filterCount > 0}
              />
          </Button>
          <ExportAnalytics />
        </>
        
        }
        insideCard={false}>
        {/*Stats Card*/}
        <div className="card mb-3 Analytics-Management">
          <StatsCard />
        </div>

        {/* Hiring Analytics */}
        <div className="row row-gap-4">
          <div className="col-xl-7 col-12">
            <HiringAnalyticsCard />
          </div>
          <div className="col-xl-5 col-12">
            <CandidatesRegisteredCard />
          </div>

          {/*Lastest Job Posted Card*/}
          {jobPermissions && jobPermissions.includes("read") && (
            <div className="col-lg-12">
              <LastestJobsPostedCard settings={settings} />
            </div>
          )}

          {interviewsPermissions && interviewsPermissions.includes("read") ? (
            <>
              <div className="col-xl-5 col-12">
                <JobStatsChartCard />
              </div>
              {/* Today's Interview Schedule */}
              <div className="col-xl-7 col-12">
                <TodayInterviewSchedule />
              </div>
            </>
          ) : (
            <div className="col-lg-12 col-12">
              <JobStatsChartCard />
            </div>
          )}
        </div>
      </PageHeader>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    { wildcard: true },
    "analytics_management",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

AnalyticsManagementPage.layout = PrivateLayout;
