import React, { useState } from 'react';
import { But<PERSON> } from 'react-bootstrap';
import { useAppDispatch } from '@src/redux/store';
import { openDialog } from '@src/redux/actions';
import DialogComponents from '@src/components/DialogComponents';
import { KeyPairInterface } from '@src/redux/interfaces';
import { Badge } from 'antd';
import { Tune } from '@mui/icons-material';

/**
 * Example component demonstrating how to use the AnalyticsFilterModal
 * This shows the complete implementation pattern used throughout the application
 */
export const AnalyticsFilterModalExample: React.FC = () => {
  const dispatch = useAppDispatch();
  
  // State to manage current filters
  const [filterState, setFilterState] = useState<KeyPairInterface>({});
  const [filterCount, setFilterCount] = useState(0);

  /**
   * Function to handle filter changes from the modal
   * This gets called when user clicks "Apply Filters" in the modal
   */
  const setFilters = async (filters: KeyPairInterface) => {
    // Update the filter state
    setFilterState(filters);
    
    // Count non-empty filter values for badge display
    const count = Object.values(filters).filter(value => 
      value !== null && value !== undefined && value !== ""
    ).length;
    setFilterCount(count);
    
    // Here you would typically:
    // 1. Update your data based on the filters
    // 2. Make API calls with the new filters
    // 3. Update charts/tables/components with filtered data
    console.log("Applied filters:", filters);
    
    // Example of what you might do:
    // await fetchAnalyticsData(filters);
    // updateCharts(filters);
    // refreshTables(filters);
  };

  /**
   * Function to open the filter modal
   * This follows the standard pattern used throughout the application
   */
  const handleFilterModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.ANALYTICS_FILTER_MODAL, // The modal configuration
        options: {
          title: "Analytics Filters", // Modal title
          filters: filterState, // Current filter state to pre-populate the modal
          onConfirm: setFilters, // Callback function when filters are applied
        },
      }),
    );
  };

  /**
   * Function to clear all filters
   */
  const clearFilters = () => {
    setFilters({});
  };

  return (
    <div className="analytics-filter-example">
      <h3>Analytics Filter Modal Example</h3>
      
      <div className="d-flex gap-2 mb-3">
        {/* Filter Button with Badge */}
        <Button
          onClick={handleFilterModal}
          className="btn btn-border d-flex align-items-center justify-content-between px-3">
          <Tune className="me-1" />
          <span>Filters</span>
          <Badge
            count={filterCount}
            showZero={false}
            dot={filterCount > 0}
          />
        </Button>

        {/* Clear Filters Button */}
        {filterCount > 0 && (
          <Button
            onClick={clearFilters}
            className="btn btn-outline-secondary">
            Clear Filters
          </Button>
        )}
      </div>

      {/* Display Current Filters */}
      {filterCount > 0 && (
        <div className="current-filters mb-3">
          <h5>Current Filters:</h5>
          <div className="d-flex flex-wrap gap-2">
            {Object.entries(filterState).map(([key, value]) => {
              if (value !== null && value !== undefined && value !== "") {
                return (
                  <Badge key={key} color="blue">
                    {key}: {String(value)}
                  </Badge>
                );
              }
              return null;
            })}
          </div>
        </div>
      )}

      {/* Example Content that would be filtered */}
      <div className="filtered-content">
        <h5>Analytics Content (would be filtered based on above filters)</h5>
        <p>This is where your analytics charts, tables, and data would be displayed.</p>
        <p>The content would update based on the filters applied above.</p>
      </div>

      {/* Usage Instructions */}
      <div className="usage-instructions mt-4">
        <h5>How to Use This Pattern:</h5>
        <ol>
          <li>Import the necessary dependencies (useAppDispatch, openDialog, DialogComponents)</li>
          <li>Create state for filters and filter count</li>
          <li>Create a setFilters function to handle filter changes</li>
          <li>Create a handleFilterModal function to open the modal</li>
          <li>Pass the current filters and onConfirm callback to the modal</li>
          <li>Use the filter state to update your data/components</li>
        </ol>
      </div>
    </div>
  );
};

export default AnalyticsFilterModalExample;
