import React, { useState } from 'react';
import { KeyPairInterface } from "@src/redux/interfaces";
import { Tabs } from "antd";
import { AnalyticsFilter, AnalyticsFilterHistory } from '../WildCard';

type AnalyticsFiltersModalProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void;
  close: Function;
};

export const AnalyticsFilterModal = (props: AnalyticsFiltersModalProps) => {
  const [filters, setFilters] = useState<KeyPairInterface>(props.filters || {});

  const handleConfirm = (newFilters: KeyPairInterface) => {
    // Map the filter fields appropriately for analytics
    const processedFilters = {
      department_id: newFilters.department_id,
      opportunity_id: newFilters.opportunity_id,
      date_from: newFilters.date_from,
      date_to: newFilters.date_to,
      status: newFilters.status,
      ...newFilters, // Include any additional filters
    };
    setFilters(processedFilters);
    props.onConfirm(processedFilters);
  };

  return (
    <Tabs
      defaultActiveKey="Filter"
      animated={{ tabPane: true, inkBar: true }}
      items={[
        {
          key: "Filter",
          label: `New Search`,
          children: <AnalyticsFilter {...props} filters={filters} onConfirm={handleConfirm} />,
        },
        {
          key: "History",
          label: `Search History`,
          children: (
            <AnalyticsFilterHistory {...props} onConfirm={handleConfirm} />
          ),
        },
      ]}
    />
  );
};