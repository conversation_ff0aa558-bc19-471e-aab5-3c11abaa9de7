import React, { useEffect, useState } from "react";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
    getAllDepartmentOptions,
  getAllOpportunityOptions,
} from "@src/redux/actions";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";

type AnalyticsFilterProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void; // Function to call when the confirmation button is clicked
  close: Function; // Function to close the modal
};


// Analytics Filter Component
export const AnalyticsFilter = ({
  filters,
  close,
   ,
}: AnalyticsFilterProps) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>(filters);
  const [loading, setLoading] = useState<boolean>(false);
  const opportunityOptions = useSelector(
    (state: RootState) => state.opportunity.options,
  );

  const departmentOptions = useSelector(
    (state: RootState) => state.department.options,
  );

  console.log(departmentOptions, ":::departmentOptions", opportunityOptions);
  

  useEffect(() => {
    fetchDepartmentOptions("", 1);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  const fetchOpportunitiesOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    showId = search == "" ? filters.opportunity_id : showId;
    await setLoading(true);
    await dispatch(getAllOpportunityOptions({ search, showId }));
    await setLoading(false);
  };

  const fetchDepartmentOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    await setLoading(true);
    await dispatch(getAllDepartmentOptions({ search, showId }));
    await setLoading(false);
  };

  const FilterFields : GlobalInputFieldType[] = [
    {
      name: "department_id",
      label: "Department",
      placeholder: "Select Department",
      options: departmentOptions,
      onSearch: fetchOpportunitiesOptions,
      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
    },
    {
      name: "opportunity_id",
      label: "Job",
      placeholder: "Select Job",
      options: opportunityOptions,
      onSearch: fetchOpportunitiesOptions,
      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
    },


  ];

  const onSubmit = (state: KeyPairInterface) => {
    onConfirm(state);
    close();
  };

  return (
    <>
      <ModalFormInput
        state={state}
        setState={setState}
        fields={FilterFields}
        onSubmit={() => onSubmit(state)}
        onClose={() => onSubmit({})}
        buttonTitle="Apply Filters"
        cancelButtonTitle="Clear Filters"
        
      />
    </>
  );
};
